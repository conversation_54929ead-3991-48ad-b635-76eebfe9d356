using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EduTrackForWin.Models
{
    public class Session
    {
        [Key]
        public int Id { get; set; }

        public DateTime Date { get; set; }

        public TimeSpan StartTime { get; set; }

        public TimeSpan EndTime { get; set; }

        [StringLength(100)]
        public string Room { get; set; } = string.Empty;

        [StringLength(500)]
        public string Notes { get; set; } = string.Empty;

        [StringLength(200)]
        public string Topic { get; set; } = string.Empty;

        public bool IsCompleted { get; set; } = false;

        public bool TeacherPresent { get; set; } = true;

        // Foreign Keys
        public int GroupId { get; set; }
        public int TeacherId { get; set; }
        public int? ScheduleId { get; set; } // يمكن أن تكون الحصة جزءًا من جدول زمني أو حصة فردية

        // Navigation Properties
        [ForeignKey("GroupId")]
        public virtual Group Group { get; set; } = null!;

        [ForeignKey("TeacherId")]
        public virtual Teacher Teacher { get; set; } = null!;

        [ForeignKey("ScheduleId")]
        public virtual Schedule? Schedule { get; set; }

        public virtual ICollection<Attendance> Attendances { get; set; } = new List<Attendance>();

        // Computed Properties
        [NotMapped]
        public TimeSpan Duration => EndTime - StartTime;

        [NotMapped]
        public int PresentStudents => Attendances?.Count(a => a.IsPresent) ?? 0;

        [NotMapped]
        public int AbsentStudents => Attendances?.Count(a => !a.IsPresent) ?? 0;

        [NotMapped]
        public double AttendanceRate => Attendances?.Count > 0 ? (double)PresentStudents / Attendances.Count * 100 : 0;

        [NotMapped]
        public string SessionTime => $"{StartTime:hh\\:mm} - {EndTime:hh\\:mm}";

        [NotMapped]
        public bool IsPartOfSchedule => ScheduleId.HasValue;

        public DateTime CreatedAt { get; set; } = DateTime.Now;
        public DateTime? UpdatedAt { get; set; }

        // Recurrence Properties
        public bool IsRecurring { get; set; } = false;
        public string RecurrenceType { get; set; } = string.Empty; // Daily, Weekly, Monthly, etc.
        public DateTime? RecurrenceEndDate { get; set; }
    }
}
