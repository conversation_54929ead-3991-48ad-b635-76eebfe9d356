using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EduTrackForWin.Models
{
    public class Schedule
    {
        [Key]
        public int ScheduleId { get; set; }
        
        [NotMapped]
        public int Id => ScheduleId;

        [Required]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;

        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }

        public bool Sunday { get; set; } = false;
        public bool Monday { get; set; } = false;
        public bool Tuesday { get; set; } = false;
        public bool Wednesday { get; set; } = false;
        public bool Thursday { get; set; } = false;
        public bool Friday { get; set; } = false;
        public bool Saturday { get; set; } = false;

        public TimeSpan StartTime { get; set; }
        public TimeSpan EndTime { get; set; }

        [StringLength(100)]
        public string Room { get; set; } = string.Empty;

        [StringLength(200)]
        public string Topic { get; set; } = string.Empty;

        [StringLength(500)]
        public string Notes { get; set; } = string.Empty;

        // Foreign Keys
        public int GroupId { get; set; }
        public int TeacherId { get; set; }

        // Navigation Properties
        [ForeignKey("GroupId")]
        public virtual Group Group { get; set; } = null!;

        [ForeignKey("TeacherId")]
        public virtual Teacher Teacher { get; set; } = null!;

        // Collection of sessions generated from this schedule
        public virtual ICollection<Session> Sessions { get; set; } = new List<Session>();

        public DateTime CreatedAt { get; set; } = DateTime.Now;
        public DateTime? UpdatedAt { get; set; }

        [NotMapped]
        public string ScheduleTime => $"{StartTime:hh\\:mm} - {EndTime:hh\\:mm}";

        [NotMapped]
        public string DaysOfWeek
        {
            get
            {
                var days = new List<string>();
                if (Sunday) days.Add("الأحد");
                if (Monday) days.Add("الإثنين");
                if (Tuesday) days.Add("الثلاثاء");
                if (Wednesday) days.Add("الأربعاء");
                if (Thursday) days.Add("الخميس");
                if (Friday) days.Add("الجمعة");
                if (Saturday) days.Add("السبت");
                return string.Join(", ", days);
            }
        }
    }
}