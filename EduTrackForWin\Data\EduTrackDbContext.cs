using Microsoft.EntityFrameworkCore;
using EduTrackForWin.Models;
using System.IO;

namespace EduTrackForWin.Data
{
    public class EduTrackDbContext : DbContext
    {
        public DbSet<Student> Students { get; set; }
        public DbSet<Teacher> Teachers { get; set; }
        public DbSet<Group> Groups { get; set; }
        public DbSet<Session> Sessions { get; set; }
        public DbSet<Schedule> Schedules { get; set; }
        public DbSet<Attendance> Attendances { get; set; }
        public DbSet<Payment> Payments { get; set; }
        public DbSet<Exam> Exams { get; set; }
        public DbSet<ExamResult> ExamResults { get; set; }
        public DbSet<Settings> Settings { get; set; }

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            var dbPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), 
                                    "EduTrackForWin", "edutrack.db");
            
            // Ensure directory exists
            var directory = Path.GetDirectoryName(dbPath);
            if (!Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory!);
            }

            optionsBuilder.UseSqlite($"Data Source={dbPath}");
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // Configure relationships
            modelBuilder.Entity<Student>()
                .HasOne(s => s.Group)
                .WithMany(g => g.Students)
                .HasForeignKey(s => s.GroupId)
                .OnDelete(DeleteBehavior.SetNull);

            modelBuilder.Entity<Group>()
                .HasOne(g => g.Teacher)
                .WithMany(t => t.Groups)
                .HasForeignKey(g => g.TeacherId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<Session>()
                .HasOne(s => s.Group)
                .WithMany(g => g.Sessions)
                .HasForeignKey(s => s.GroupId)
                .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<Session>()
                .HasOne(s => s.Teacher)
                .WithMany(t => t.Sessions)
                .HasForeignKey(s => s.TeacherId)
                .OnDelete(DeleteBehavior.Restrict);
                
            modelBuilder.Entity<Session>()
                .HasOne(s => s.Schedule)
                .WithMany(sc => sc.Sessions)
                .HasForeignKey(s => s.ScheduleId)
                .OnDelete(DeleteBehavior.SetNull);
                
            modelBuilder.Entity<Schedule>()
                .HasOne(sc => sc.Group)
                .WithMany()
                .HasForeignKey(sc => sc.GroupId)
                .OnDelete(DeleteBehavior.Cascade);
                
            modelBuilder.Entity<Schedule>()
                .HasOne(sc => sc.Teacher)
                .WithMany()
                .HasForeignKey(sc => sc.TeacherId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<Attendance>()
                .HasOne(a => a.Student)
                .WithMany(s => s.Attendances)
                .HasForeignKey(a => a.StudentId)
                .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<Attendance>()
                .HasOne(a => a.Session)
                .WithMany(s => s.Attendances)
                .HasForeignKey(a => a.SessionId)
                .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<Payment>()
                .HasOne(p => p.Student)
                .WithMany(s => s.Payments)
                .HasForeignKey(p => p.StudentId)
                .OnDelete(DeleteBehavior.Cascade);

            // Configure Exam relationships
            modelBuilder.Entity<Exam>()
                .HasOne(e => e.Group)
                .WithMany()
                .HasForeignKey(e => e.GroupId)
                .OnDelete(DeleteBehavior.Cascade);

            // Configure ExamResult relationships
            modelBuilder.Entity<ExamResult>()
                .HasOne(er => er.Exam)
                .WithMany(e => e.ExamResults)
                .HasForeignKey(er => er.ExamId)
                .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<ExamResult>()
                .HasOne(er => er.Student)
                .WithMany()
                .HasForeignKey(er => er.StudentId)
                .OnDelete(DeleteBehavior.Cascade);

            // Ensure unique exam result per student per exam
            modelBuilder.Entity<ExamResult>()
                .HasIndex(er => new { er.ExamId, er.StudentId })
                .IsUnique();

            // Configure indexes for better performance
            modelBuilder.Entity<Student>()
                .HasIndex(s => s.Name);

            modelBuilder.Entity<Teacher>()
                .HasIndex(t => t.Name);

            modelBuilder.Entity<Group>()
                .HasIndex(g => g.Name);

            modelBuilder.Entity<Session>()
                .HasIndex(s => s.Date);

            modelBuilder.Entity<Attendance>()
                .HasIndex(a => a.Date);

            modelBuilder.Entity<Payment>()
                .HasIndex(p => p.PaymentDate);
                
            modelBuilder.Entity<Schedule>()
                .HasIndex(sc => sc.StartDate);
                
            modelBuilder.Entity<Schedule>()
                .HasIndex(sc => sc.EndDate);

            // Seed default settings
            modelBuilder.Entity<Settings>().HasData(
                new Settings
                {
                    Id = 1,
                    CenterName = "مركز EduTrack التعليمي",
                    CenterAddress = "العنوان",
                    CenterPhone = "123456789",
                    CenterEmail = "<EMAIL>",
                    DefaultMonthlyFee = 100,
                    DarkMode = false,
                    AutoBackup = true,
                    BackupIntervalDays = 1,
                    MaxAbsenceWarning = 3,
                    ReportHeader = "تقرير مركز EduTrack التعليمي",
                    ReportFooter = "شكراً لثقتكم بنا"
                });
        }
    }
}
